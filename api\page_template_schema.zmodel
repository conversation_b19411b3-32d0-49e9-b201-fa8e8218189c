// Схема шаблонов страниц (типизированные JSON поля ZenStack)

// Типы конфигураций

type AttributeListConfig {
    // Системные имена атрибутов из AttributeTemplate.name
    attributeNames String[]
    // Необязательный пользовательский порядок по именам
    sortOrder     String[]
    // Отображать единицы измерения при форматировании
    withUnits     Boolean @default(false)
}

type CategoryTemplateConfig {
    h1           String?
    h2           String?
    description  String?
    footer       String?
    // Список атрибутов, доступных как фильтры на странице категории
    filters      AttributeListConfig?
    // Какие атрибуты показывать в карточках/таблицах товаров внутри категории
    productAttrs AttributeListConfig?
}

type PartTemplateConfig {
    h1          String?
    h2          String?
    attributes  AttributeListConfig
}

type CatalogItemTemplateConfig {
    h1          String?
    h2          String?
    attributes  AttributeListConfig
}

enum TemplateKind {
    CATEGORY
    PART
    CATALOG_ITEM
}

model PageTemplate {
    id              String   @id @default(uuid())
    name            String   @length(1, 150)
    description     String?  @length(1, 1000)

    kind            TemplateKind

    // Для CATEGORY шаблонов можно привязать к конкретной категории по ID,
    // чтобы избежать жесткой зависимости от модели в другой схеме
    partCategoryId  Int?

    isActive        Boolean  @default(true)
    isDefault       Boolean  @default(false)

    // Типизированные JSON поля (ZenStack @json)
    categoryConfig      CategoryTemplateConfig?     @json
    partConfig          PartTemplateConfig?         @json
    catalogItemConfig   CatalogItemTemplateConfig?  @json

    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt

    @@index([kind])
    @@index([partCategoryId])

    // Политики доступа: чтение всем, управление — только ADMIN
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

