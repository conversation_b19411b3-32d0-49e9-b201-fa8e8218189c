import type { PrismaClient, PageTemplate as PrismaPageTemplate } from '@prisma/client';
import { CategoryTemplateConfigSchema, PartTemplateConfigSchema, CatalogItemTemplateConfigSchema } from '../schemas/page-templates';
import { buildPartAttributeDict, buildCatalogItemAttributeDict } from '../lib/attributes';

export class PageTemplatesService {
  constructor(private readonly db: PrismaClient) {}

  async getTemplateByKind(kind: 'CATEGORY' | 'PART' | 'CATALOG_ITEM', opts?: { partCategoryId?: number; fallbackDefault?: boolean }): Promise<PrismaPageTemplate | null> {
    const { partCategoryId, fallbackDefault = true } = opts ?? {};

    if (kind === 'CATEGORY' && partCategoryId) {
      const found = await this.db.pageTemplate.findFirst({ where: { kind, isActive: true, partCategoryId } });
      if (found) return found;
    }

    if (fallbackDefault) {
      const def = await this.db.pageTemplate.findFirst({ where: { kind, isActive: true, isDefault: true } });
      if (def) return def;
    }

    return null;
  }

  // Данные для страницы категории
  async getCategoryRenderDataBySlug(slug: string) {
    const category = await this.db.partCategory.findFirstOrThrow({ where: { slug } });

    const tpl = await this.getTemplateByKind('CATEGORY', { partCategoryId: category.id, fallbackDefault: true });
    if (!tpl || !tpl.categoryConfig) {
      return { template: null, category, data: { category }, attr: null };
    }

    const cfg = CategoryTemplateConfigSchema.parse(tpl.categoryConfig as unknown);

    return { template: { id: tpl.id, kind: tpl.kind, config: cfg }, category, data: { category }, attr: null };
  }

  // Данные для страницы Part
  async getPartRenderDataById(id: number) {
    const part = await this.db.part.findFirstOrThrow({
      where: { id },
      include: {
        attributes: { include: { template: true } },
        partCategory: true,
        image: true,
        mediaAssets: true,
      },
    });

    const tpl = await this.getTemplateByKind('PART');

    const attr = buildPartAttributeDict(part.attributes as unknown as any[]);
    const cfg = tpl?.partConfig ? PartTemplateConfigSchema.parse(tpl.partConfig as unknown) : null;

    return { template: tpl ? { id: tpl.id, kind: tpl.kind, config: cfg } : null, part, data: { part }, attr };
  }

  // Данные для страницы CatalogItem
  async getCatalogItemRenderDataById(id: number) {
    const item = await this.db.catalogItem.findFirstOrThrow({
      where: { id },
      include: {
        attributes: { include: { template: true } },
        brand: true,
        image: true,
        mediaAssets: true,
      },
    });

    const tpl = await this.getTemplateByKind('CATALOG_ITEM');

    const attr = buildCatalogItemAttributeDict(item.attributes as unknown as any[]);
    const cfg = tpl?.catalogItemConfig ? CatalogItemTemplateConfigSchema.parse(tpl.catalogItemConfig as unknown) : null;

    return { template: tpl ? { id: tpl.id, kind: tpl.kind, config: cfg } : null, item, data: { item, brand: item.brand }, attr };
  }
}

